<script lang="ts">
  import type { PostEntity } from "./[id]/types";

  import { onMount } from "svelte";
  import { fetchWithAuth } from "$lib";
  import PostCard from "./post-card.svelte";
  import RightMenu from "./right-menu.svelte";

  const { data } = $props();

  const { locale } = data;

  let page = $state(1);
  let posts = $state<PostEntity[]>([]);

  onMount(async function fetchPosts() {
    const response = await fetchWithAuth(`/api/reactor/post?page=${page}&size=20`);

    if (response.ok) {
      const data = (await response.json()) as {
        items: PostEntity[];
        total: number;
      };

      const fetchedPosts = data.items.map((post) => ({
        ...post,

        createdAt: new Date(post.createdAt),
        updatedAt: new Date(post.updatedAt),
      }));

      posts.push(...fetchedPosts);
    }
  });
</script>

<div class="row g-4 mt-3">
  <div class="col-1"></div>

  <!-- Left Menu (2-3 columns) -->
  <div class="col-2">
    <!-- <LeftMenu {locale} /> -->
  </div>

  <!-- Feed (4-9 columns) -->
  <div class="col-6">
    <div class="feed">
      {#if posts.length}
        {#each posts as post (post.id)}
          <PostCard {post} {locale} />
        {/each}
      {:else}
        <div class="text-center">
          <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Right Menu (10-11 columns) -->
  <div class="col-2">
    <RightMenu {locale} />
  </div>
</div>

<style>
  .feed {
    max-width: 100%;
  }

  @media (max-width: 767.98px) {
    .feed {
      margin-top: 1rem;
    }
  }
</style>
