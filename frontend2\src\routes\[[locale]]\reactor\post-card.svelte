<script lang="ts">
  import type { Locale } from "$lib";
  import type { PostEntity, RatingStatus } from "./[id]/types";

  import { fetchWithAuth, findLocalizationForUserLocales } from "$lib";

  interface Props {
    post: PostEntity;
    locale: Locale;
  }

  const { post, locale }: Props = $props();

  const i18n = {
    en: {
      usefulnesss: "Usefulness",
      comments: "Comments",
      commentPlaceholder: "Write your comment...",
      submit: "Submit",
      time: {
        days(n: number) {
          return `${n} ${n === 1 ? "day" : "days"} ago`;
        },
        hours(n: number) {
          return `${n} ${n === 1 ? "hour" : "hours"} ago`;
        },
        minutes(n: number) {
          return `${n} ${n === 1 ? "minute" : "minutes"} ago`;
        },
        seconds(n: number) {
          return `${n} ${n === 1 ? "second" : "seconds"} ago`;
        },
        rightNow: "right now",
      },
    },
    ru: {
      usefulnesss: "Полезность",
      comments: "Комментарии",
      commentPlaceholder: "Напишите ваш комментарий...",
      submit: "Отправить",
      time: {
        getPlural(n: number) {
          if (n === 1) return 0;
          if (n >= 2 && n <= 4) return 1;

          return 2;
        },

        days(n: number) {
          const word = ["день", "дня", "дней"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        hours(n: number) {
          const word = ["час", "часа", "часов"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        minutes(n: number) {
          const word = ["минуту", "минуты", "минут"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        seconds(n: number) {
          const word = ["секунду", "секунды", "секунд"][this.getPlural(n)];

          return `${n} ${word} назад`;
        },

        rightNow: "только что",
      },
    },
  };

  const t = $derived(i18n[locale]);

  let rating = $state<PostEntity["rating"]>(post.rating);
  let usefulness = $state<PostEntity["usefulness"]>(post.usefulness);

  const ratingValue = $derived(rating.likes - rating.dislikes);
  const usefulnessValue = $derived(usefulness.totalValue ?? 0);

  // Hover state for usefulness stars
  let hoveredStarIndex = $state<number | null>(null);

  const authorName = $derived.by(() => findLocalizationForUserLocales(post?.author?.name ?? []));
  const title = $derived.by(() => findLocalizationForUserLocales(post?.title ?? []));
  const body = $derived.by(() => findLocalizationForUserLocales(post?.body ?? []));

  let copied = $state(false);

  // Format date for display
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffDay > 0) {
      return t.time.days(diffDay);
    } else if (diffHour > 0) {
      return t.time.hours(diffHour);
    } else if (diffMin > 0) {
      return t.time.minutes(diffMin);
    } else if (diffSec > 3) {
      return t.time.seconds(diffSec);
    } else {
      return t.time.rightNow;
    }
  }

  function getLink() {
    return `${window.location.origin}/reactor/${post.id}`;
  }

  function copyLink(evt: MouseEvent) {
    if (!evt.ctrlKey && !evt.shiftKey && !evt.altKey && !evt.metaKey) {
      evt.preventDefault();

      navigator.clipboard.writeText(getLink());

      copied = true;

      setTimeout(() => (copied = false), 2000);
    }
  }

  async function like() {
    const response = await fetchWithAuth(`/api/reactor/post/${post.id}/rating`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "like",
      }),
    });

    if (response.ok) {
      rating = (await response.json()) as {
        likes: number;
        dislikes: number;
        status: RatingStatus | null;
      };
    }
  }

  async function dislike() {
    const response = await fetchWithAuth(`/api/reactor/post/${post.id}/rating`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "dislike",
      }),
    });

    if (response.ok) {
      rating = (await response.json()) as {
        likes: number;
        dislikes: number;
        status: RatingStatus | null;
      };
    }
  }

  async function rateUsefulness(value: number) {
    const response = await fetchWithAuth(`/api/reactor/post/${post.id}/usefulness`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        value: value === usefulness?.value ? null : value,
      }),
    });

    if (response.ok) {
      usefulness = (await response.json()) as {
        value: number | null;
        count: number;
        totalValue: number | null;
      };
    }
  }
</script>

<div class="post-card mb-4">
  <div class="card">
    <div class="card-header">
      <!-- Post Header with Author and Time -->
      <div class="post-header d-flex justify-content-between align-items-center mb-3">
        <div class="d-flex align-items-center">
          <!-- Rating -->
          <div class="rating-block d-flex align-items-center me-3">
            {#if ratingValue > 0}
              <span class="rating-value me-2 text-success">{ratingValue}</span>
            {:else if ratingValue < 0}
              <span class="rating-value me-2 text-danger">{ratingValue}</span>
            {:else}
              <span class="rating-value me-2">0</span>
            {/if}
            <div class="rating-buttons">
              <button
                class={`btn btn-sm me-1 ${rating?.status === "like" ? "btn-success" : "btn-outline-success"}`}
                aria-label="Like"
                onclick={like}
              >
                <i class="bi bi-hand-thumbs-up"></i>
              </button>
              <button
                class={`btn btn-sm ${rating?.status === "dislike" ? "btn-danger" : "btn-outline-danger"}`}
                aria-label="Dislike"
                onclick={dislike}
              >
                <i class="bi bi-hand-thumbs-down"></i>
              </button>
            </div>
          </div>

          <!-- Author info -->
          <div class="author-info d-flex align-items-center">
            <div>
              <div class="author-name fw-bold">
                {authorName ?? "Anonymous"}
              </div>
              <div class="post-time small text-muted" title={post.createdAt.toISOString()}>
                {formatDate(post.createdAt.toISOString())}
              </div>
            </div>
          </div>
        </div>

        <div class="usefulness-block">
          <div class="d-flex flex-column align-items-start">
            {#if usefulness && usefulness.count > 0}
              <span class="usefulness-label mb-1 text-muted small px-1">
                {t.usefulnesss} ({usefulness.count})
              </span>
            {:else}
              <span class="usefulness-label mb-1 text-muted small px-1">
                {t.usefulnesss}
              </span>
            {/if}

            <div
              role="group"
              aria-label="Usefulness rating"
              onmouseleave={() => (hoveredStarIndex = null)}
            >
              {#each Array(5) as _, i}
                <button
                  class="btn btn-sm p-0 px-1"
                  aria-label={`Rate usefulness ${i + 1}`}
                  onclick={() => rateUsefulness((i + 1) * 2)}
                  onmouseenter={() => (hoveredStarIndex = i)}
                >
                  <i
                    class="bi bi-star{(
                      hoveredStarIndex !== null
                        ? i <= hoveredStarIndex
                        : (i + 1) * 2 <= usefulnessValue
                    )
                      ? '-fill'
                      : ''} text-warning rating-star"
                  ></i>
                </button>
              {/each}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card-body">
      <h5 class="card-title">{title}</h5>

      <p class="card-text">{body}</p>

      <div class="tags mb-3">
        {#each [...post.tags, "test"] as tag, i}
          <span class="badge bg-light text-secondary me-1">tag #{i + 1}: {tag}</span>
        {/each}
      </div>

      <div class="card-actions d-flex">
        <!-- <button class="btn btn-sm btn-outline-secondary me-2" aria-label="Save">
        <i class="bi bi-bookmark"></i>
      </button> -->
        <a href={getLink()} target="_blank">
          <button
            class={`btn btn-sm ${copied ? "btn-success" : "btn-outline-secondary"}`}
            aria-label="Copy link"
            onclick={copyLink}
          >
            {#if copied}
              <i class="bi bi-check-circle"></i>
            {:else}
              <i class="bi bi-link-45deg"></i>
            {/if}
          </button>
        </a>
      </div>
    </div>
  </div>
</div>

<style>
  .post-card {
    transition: all 0.2s ease;
  }

  .rating-value {
    font-weight: bold;
    min-width: 30px;
    text-align: center;
  }

  .card-title {
    font-weight: 600;
  }

  .text-gradient-overlay {
    position: relative;
    height: 20px;
    margin-top: -20px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
  }

  .tags {
    margin-top: 1rem;
  }

  .badge {
    font-weight: 500;
    padding: 0.5em 0.7em;
  }
</style>
