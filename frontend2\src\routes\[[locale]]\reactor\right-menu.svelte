<script lang="ts">
  import type { Locale } from "$lib";
  import Modal from "$lib/components/modal.svelte";

  interface Props {
    locale: Locale;
  }

  const i18n = {
    en: {
      sections: "Sections",
      answers: "Answers",
      myComments: "My Comments",
      ratings: "Ratings",
      saved: "Saved",
      viewed: "Viewed",
      settings: "Settings",
      toggleSections: "Toggle sections",
      createPost: "Create Post",
      createPostTitle: "Create New Post",
      cancel: "Cancel",
      create: "Create",
    },
    ru: {
      sections: "Разделы",
      answers: "Ответы",
      myComments: "Мои комментарии",
      ratings: "Оценки",
      saved: "Сохраненное",
      viewed: "Просмотренное",
      settings: "Настройки",
      toggleSections: "Переключить разделы",
      createPost: "Создать пост",
      createPostTitle: "Создать новый пост",
      cancel: "Отмена",
      create: "Создать",
    },
  };

  const { locale }: Props = $props();

  const t = i18n[locale];

  // State
  let isExpanded = $state(true);
  let showCreatePostModal = $state(false);

  function toggleIsExpanded() {
    isExpanded = !isExpanded;
  }

  function openCreatePostModal() {
    showCreatePostModal = true;
  }

  function closeCreatePostModal() {
    showCreatePostModal = false;
  }

  function handleCreatePost() {
    // TODO: Implement post creation logic
    console.log("Create post functionality will be implemented later");
    closeCreatePostModal();
  }
</script>

<div class="right-menu {!isExpanded ? 'collapsed' : ''}" role="navigation" aria-label={t.sections}>
  <div class="right-menu-header d-flex justify-content-between align-items-center p-3 bg-light">
    <h6 class="mb-0">{t.sections}</h6>
    <button
      class="btn btn-sm btn-link p-0"
      onclick={toggleIsExpanded}
      aria-label={t.toggleSections}
    >
      <i class="bi bi-chevron-{isExpanded ? 'up' : 'down'}"></i>
    </button>
  </div>

  {#if isExpanded}
    <div class="right-menu-content p-3">

      <ul class="list-group list-group-flush">
        <li class="list-group-item border-0 px-0 ps-1">
          <a href="/reactor/answers" class="text-decoration-none text-body">
            <i class="bi bi-chat-dots me-2"></i>
            {t.answers}
          </a>
        </li>
        <li class="list-group-item border-0 px-0 ps-1">
          <a href="/reactor/my-comments" class="text-decoration-none text-body">
            <i class="bi bi-chat-left-text me-2"></i>
            {t.myComments}
          </a>
        </li>
        <li class="list-group-item border-0 px-0 ps-1">
          <a href="/reactor/rates" class="text-decoration-none text-body">
            <i class="bi bi-star me-2"></i>
            {t.ratings}
          </a>
        </li>
        <li class="list-group-item border-0 px-0 ps-1">
          <a href="/reactor/saved" class="text-decoration-none text-body">
            <i class="bi bi-bookmark me-2"></i>
            {t.saved}
          </a>
        </li>
        <li class="list-group-item border-0 px-0 ps-1">
          <a href="/reactor/seen" class="text-decoration-none text-body">
            <i class="bi bi-eye me-2"></i>
            {t.viewed}
          </a>
        </li>
        <li class="list-group-item border-0 px-0 ps-1">
          <a href="/reactor/settings" class="text-decoration-none text-body">
            <i class="bi bi-gear me-2"></i>
            {t.settings}
          </a>
        </li>
      </ul>
    </div>
  {/if}
</div>

<!-- Create Post Modal -->
<Modal
  show={showCreatePostModal}
  title={t.createPostTitle}
  onClose={closeCreatePostModal}
  onSubmit={handleCreatePost}
  submitText={t.create}
  cancelText={t.cancel}
  showFooter={false}
>
  <p>Post creation form will be implemented here.</p>
</Modal>

<style lang="scss">
  .right-menu {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    overflow: hidden;
    transition: opacity 0.3s ease;

    &.collapsed {
      opacity: 0.5;

      &:hover {
        opacity: 1;
      }
    }
  }

  .right-menu-header {
    border-bottom: 1px solid #dee2e6;
  }

  .list-group-item {
    background-color: transparent;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    a {
      display: block;
    }
  }

  .create-post-btn {
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--bs-success);
      color: var(--bs-success);
      background-color: transparent;
    }
  }
</style>
